import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';

export const PUT: RequestHandler = async ({ params, request, locals: { supabase } }) => {
    try {
        const data = await request.json();

        console.log('Data to update: ', data);
        console.log('Guest ID: ', params.id);

        // Validate required fields
        if (!data.firstName) {
            return json({ error: 'First name is required' }, { status: 400 });
        }

        // Helper function to capitalize names properly
        const capitalizeName = (name: string): string => {
            if (!name) return '';
            return name.trim()
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');
        };

        // Prepare the update data with proper capitalization
        const updateData = {
            first_name: capitalizeName(data.firstName),
            last_name: data.lastName ? capitalizeName(data.lastName) : null,
            email: data.email || null,
            mobile: data.mobile || null,
            whatsapp: data.whatsapp || null,
            tshirt_size: data.tshirtSize ? data.tshirtSize.toUpperCase() : null,
            category: data.category ? data.category.toUpperCase() : null,
            updated_at: new Date().toISOString()
        };

        console.log('Processed update data:', updateData);

        const { error: updateError } = await supabase
            .from('workforce')
            .update(updateData)
            .eq('id', params.id);

        if (updateError) {
            console.error('Database update error:', updateError);
            return json({ error: 'Failed to update guest data' }, { status: 500 });
        }

        return json({ success: true });
    } catch (error) {
        console.error('Error in edit-data API:', error);
        return json({ error: 'Internal server error' }, { status: 500 });
    }
};