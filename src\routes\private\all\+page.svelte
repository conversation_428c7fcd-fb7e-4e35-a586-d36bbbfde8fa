<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';
	import { goto } from '$app/navigation';
	import { formatDateTime } from '$lib/utils/formatdatetime';

	let { data }: { data: PageData } = $props();

	// Loading states
	let isSendingAll = $state(false);
	let sendingStates = $state<Record<number, boolean>>({});

	// Search and filter states
	let searchTerm = $state('');
	let emailStatusFilter = $state({ value: 'all', label: 'All Email Statuses' });
	let categoryFilter = $state({ value: 'all', label: 'All Categories' });

	// Category options based on the memories
	const categoryOptions = [
		'YOGA TRAINER',
		'ACTIVATION',
		'EMBASSY OFFICIAL',
		'VOLUNTEER',
		'ORGANISER',
		'MEDIA',
		'MEDICAL',
		'GUEST',
		'PARTICIPANT'
	];

	// Email status options
	const emailStatusOptions = ['sent', 'pending', 'failed'];

	// Filtered guests based on search and filters
	let filteredGuests = $derived.by(() => {
		let filtered = data.guests;

		// Search by name, email, or mobile
		if (searchTerm.trim()) {
			const search = searchTerm.toLowerCase().trim();
			filtered = filtered.filter((guest) => {
				const firstName = guest.first_name?.toLowerCase() || '';
				const lastName = guest.last_name?.toLowerCase() || '';
				const email = guest.email?.toLowerCase() || '';
				const mobile = guest.mobile?.toLowerCase() || '';

				return (
					firstName.includes(search) ||
					lastName.includes(search) ||
					email.includes(search) ||
					mobile.includes(search)
				);
			});
		}

		// Filter by email status
		if (emailStatusFilter.value !== 'all') {
			filtered = filtered.filter((guest) => {
				const status = guest.email_status?.toLowerCase() || 'pending';
				return status === emailStatusFilter.value;
			});
		}

		// Filter by category
		if (categoryFilter.value !== 'all') {
			filtered = filtered.filter((guest) => {
				return guest.category === categoryFilter.value;
			});
		}

		return filtered;
	});

	// Clear all filters
	function clearFilters() {
		searchTerm = '';
		emailStatusFilter = { value: 'all', label: 'All Email Statuses' };
		categoryFilter = { value: 'all', label: 'All Categories' };
	}

	// Event details - you can modify these as needed
	const eventDetails = {
		eventName: 'International Day of Yoga',
		startDate: '2025-06-21',
		endDate: '2025-06-21',
		startTime: '18:00',
		endTime: '20:00',
		location: 'New Ideal Indian School Ground',
		locationUrl: 'https://maps.app.goo.gl/RbAsa2wcG68JW7qp8'
	};

	/**
	 * Send email to a single guest
	 */
	async function sendSingleEmail(guest: any) {
		// Validate guest has required fields
		if (!guest.email) {
			alert('Guest email is missing');
			return;
		}

		if (!guest.first_name) {
			alert('Guest first name is missing');
			return;
		}

		if (!guest.qr_data) {
			alert('Guest QR data is missing');
			return;
		}

		// Set loading state for this specific guest
		sendingStates[guest.id] = true;

		try {
			const response = await fetch('/private/api/send-single-email', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					guest,
					eventDetails
				})
			});

			const result = await response.json();

			if (result.success) {
				const guestName = guest.last_name
					? `${guest.first_name} ${guest.last_name}`
					: guest.first_name;
				alert(`Email sent successfully to ${guestName}`);
				// Optionally refresh the page data to update email status
				window.location.reload();
			} else {
				const guestName = guest.last_name
					? `${guest.first_name} ${guest.last_name}`
					: guest.first_name;
				alert(`Failed to send email to ${guestName}: ${result.error}`);
			}
		} catch (error) {
			console.error('Error sending single email:', error);
			const guestName = guest.last_name
				? `${guest.first_name} ${guest.last_name}`
				: guest.first_name;
			alert(`Error sending email to ${guestName}`);
		} finally {
			sendingStates[guest.id] = false;
		}
	}

	/**
	 * Send emails to all guests (filtered)
	 */
	async function sendEmailToAll() {
		const confirmed = confirm(
			`Are you sure you want to send emails to ${filteredGuests.length} filtered guests? This action cannot be undone.`
		);

		if (!confirmed) return;

		isSendingAll = true;

		try {
			const response = await fetch('/private/api/send-all-emails', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					guests: filteredGuests,
					eventDetails
				})
			});

			const result = await response.json();

			if (result.success) {
				alert(
					`Email sending completed!\n` +
						`Total: ${result.data.total}\n` +
						`Successful: ${result.data.successful}\n` +
						`Failed: ${result.data.failed}`
				);
				// Optionally refresh the page data to update email statuses
				window.location.reload();
			} else {
				alert(`Failed to send emails: ${result.error}`);
			}
		} catch (error) {
			console.error('Error sending all emails:', error);
			alert('Error sending emails to all guests');
		} finally {
			isSendingAll = false;
		}
	}
</script>

<!-- <div class="container mx-auto p-4"> -->
<div class=" mx-auto p-4">
	<div class="flex flex-col gap-4">
		<div class="flex justify-between items-center">
			<h1 class="text-2xl font-bold">
				All Badges ({filteredGuests.length} of {data.guests.length})
			</h1>
			<Button disabled={isSendingAll} onclick={sendEmailToAll} variant="default">
				{isSendingAll ? 'Sending Emails...' : 'Send Email to All'}
			</Button>
		</div>

		<!-- Search and Filter Section -->
		<div class="p-4 bg-muted rounded-lg">
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
				<div class="space-y-2">
					<Label for="search">Search by Name, Email, or Mobile</Label>
					<Input
						id="search"
						type="text"
						placeholder="Enter search term..."
						bind:value={searchTerm}
					/>
				</div>

				<div class="space-y-2">
					<Label for="email-status">Email Status</Label>
					<Select.Root bind:selected={emailStatusFilter}>
						<Select.Trigger id="email-status">
							<Select.Value placeholder="All Email Statuses" />
						</Select.Trigger>
						<Select.Content>
							<Select.Item value="all" label="All Email Statuses">All Email Statuses</Select.Item>
							{#each emailStatusOptions as status}
								<Select.Item value={status} label={status.charAt(0).toUpperCase() + status.slice(1)}
									>{status.charAt(0).toUpperCase() + status.slice(1)}</Select.Item
								>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>

				<div class="space-y-2">
					<Label for="category">Category</Label>
					<Select.Root bind:selected={categoryFilter}>
						<Select.Trigger id="category">
							<Select.Value placeholder="All Categories" />
						</Select.Trigger>
						<Select.Content>
							<Select.Item value="all" label="All Categories">All Categories</Select.Item>
							{#each categoryOptions as category}
								<Select.Item value={category} label={category}>{category}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
				</div>
			</div>

			<!-- Clear Filters Button -->
			{#if searchTerm || emailStatusFilter.value !== 'all' || categoryFilter.value !== 'all'}
				<div class="flex justify-end">
					<Button variant="outline" size="sm" onclick={clearFilters}>Clear Filters</Button>
				</div>
			{/if}
		</div>

		<div class="rounded-lg border">
			<Table.Root>
				<Table.Header class="bg-muted">
					<Table.Row>
						<Table.Head class="border-r">#</Table.Head>

						<Table.Head>First Name</Table.Head>
						<Table.Head>Last Name</Table.Head>
						<Table.Head>Email</Table.Head>
						<Table.Head>Mobile</Table.Head>
						<Table.Head>WhatsApp</Table.Head>
						<Table.Head>QR Data</Table.Head>
						<Table.Head>Category</Table.Head>
						<Table.Head>T Shirt Size</Table.Head>
						<Table.Head>Email Status</Table.Head>
						<Table.Head>Email Sent At</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each filteredGuests as guest, index}
						<Table.Row>
							<Table.Cell class="border-r bg-muted">{index + 1}</Table.Cell>

							<Table.Cell>{guest.first_name}</Table.Cell>
							<Table.Cell>{guest.last_name}</Table.Cell>
							<Table.Cell>{guest.email}</Table.Cell>
							<Table.Cell>{guest.mobile}</Table.Cell>
							<Table.Cell>{guest.whatsapp}</Table.Cell>
							<Table.Cell>{guest.qr_data}</Table.Cell>
							<Table.Cell>{guest.category}</Table.Cell>
							<Table.Cell>{guest.tshirt_size}</Table.Cell>
							<Table.Cell>{guest.email_status}</Table.Cell>
							<Table.Cell>{formatDateTime(guest.email_sent_at)}</Table.Cell>

							<Table.Cell>
								<Button
									size="sm"
									disabled={sendingStates[guest.id] || isSendingAll}
									onclick={() => sendSingleEmail(guest)}
								>
									{sendingStates[guest.id] ? 'Sending...' : 'Send Email'}
								</Button>
								<Button size="sm" onclick={() => goto(`/private/all/edit/${guest.id}`)}>Edit</Button
								>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	</div>
</div>
